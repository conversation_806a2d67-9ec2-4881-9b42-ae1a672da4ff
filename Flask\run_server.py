#!/usr/bin/env python3
"""
Script để chạy Flask server với debug output rõ ràng
"""

import sys
import os
from pathlib import Path

# Thêm thư mục hiện tại vào Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

print("🚀 Starting Flask server...")
print(f"📁 Working directory: {current_dir}")

try:
    # Import app
    import app
    
    print(f"✅ App imported successfully")
    print(f"📦 Programs loaded: {len(getattr(app.app, 'programs', {}))}")
    
    for prog_id, prog_info in getattr(app.app, 'programs', {}).items():
        print(f"  📋 {prog_id}: {prog_info.get('name', 'Unknown')}")
    
    print("🌐 Starting server at http://localhost:5000")
    print("🔄 Press Ctrl+C to stop")
    
    # Chạy server
    app.app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
    
except KeyboardInterrupt:
    print("\n🛑 Server stopped by user")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
