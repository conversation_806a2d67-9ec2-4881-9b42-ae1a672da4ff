<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 AI Classification</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            padding: 20px;
            min-height: 100vh;
        }

        .header-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .header-section h1 {
            color: white;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header-section p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            margin: 0;
        }

        .main-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }

        .section-card {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 15px;
            margin: 20px 0;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
            background: white;
        }

        .btn {
            border-radius: 10px;
            padding: 12px 24px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2980b9, #1f5f8b);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #229954, #1e7e34);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
            color: white;
        }

        .log-area {
            background: #2c3e50;
            border: 2px solid #34495e;
            color: #2ecc71;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 13px;
            height: 300px;
            overflow-y: auto;
            padding: 15px;
            white-space: pre-wrap;
            border-radius: 10px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .progress {
            height: 8px;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            margin: 15px 0;
        }

        .progress-bar {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.7);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
        }

        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
            font-weight: 500;
        }

        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
        }

        .alert {
            border-radius: 10px;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }

        .alert-warning {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
        }

        .alert-info {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            color: #0c5460;
        }

        /* Loading spinner */
        .spinner-border {
            width: 2rem;
            height: 2rem;
        }

        /* Modal styles */
        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 20px 20px 0 0;
        }

        .modal-footer {
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 0 0 20px 20px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .header-section h1 {
                font-size: 2rem;
            }
            
            .main-card {
                padding: 20px;
            }
            
            .section-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <h1><i class="fas fa-robot"></i> AI Classification</h1>
            <p>Phân loại thông minh với AI và cập nhật Google Sheets tự động</p>
        </div>

        <div class="main-card">
            <!-- Google Sheet Information Section -->
            <div class="section-card">
                <div class="section-title">
                    <i class="fas fa-table"></i>
                    Google Sheet Information
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-8">
                        <label class="form-label"><i class="fas fa-link"></i> Spreadsheet URL:</label>
                        <input type="text" id="sheetUrl" class="form-control" 
                               placeholder="Nhập Google Spreadsheet URL">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button id="loadSheetBtn" class="btn btn-primary w-100">
                            <i class="fas fa-download"></i> Load Sheet
                        </button>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label"><i class="fas fa-file-alt"></i> Sheet:</label>
                        <select id="sheetSelect" class="form-control" disabled>
                            <option value="">Chọn sheet...</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label"><i class="fas fa-cog"></i> AI Model:</label>
                        <select id="modelSelect" class="form-control">
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            <option value="gpt-4">GPT-4</option>
                            <option value="claude-3">Claude 3</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Processing Section -->
            <div class="section-card">
                <div class="section-title">
                    <i class="fas fa-play-circle"></i>
                    AI Processing
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-4">
                        <button id="startBtn" class="btn btn-success w-100" disabled>
                            <i class="fas fa-rocket"></i> Bắt đầu phân loại
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button id="pauseBtn" class="btn btn-secondary w-100" disabled>
                            <i class="fas fa-pause"></i> Tạm dừng
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button id="stopBtn" class="btn btn-danger w-100" disabled>
                            <i class="fas fa-stop"></i> Dừng
                        </button>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="progress">
                    <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>

                <!-- Statistics -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalItems">0</div>
                        <div class="stat-label">Tổng số items</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="processedItems">0</div>
                        <div class="stat-label">Đã xử lý</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="successItems">0</div>
                        <div class="stat-label">Thành công</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="errorItems">0</div>
                        <div class="stat-label">Lỗi</div>
                    </div>
                </div>
            </div>

            <!-- Log Section -->
            <div class="section-card">
                <div class="section-title">
                    <i class="fas fa-terminal"></i>
                    Processing Log
                </div>
                <div id="logArea" class="log-area"></div>
                <div class="mt-3">
                    <button id="clearLogBtn" class="btn btn-secondary">
                        <i class="fas fa-trash"></i> Xóa log
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/ai_classification.js') }}"></script>
</body>
</html>
