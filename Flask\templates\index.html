<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Assortment All in One</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <div id="app">
        <header>
            <div class="logo">
                <button @click="showProgramList = !showProgramList" class="program-list-button">
                    <i class="fas fa-th"></i> Chương trình
                </button>
            </div>

            <!-- Tab bar integrated into header -->
            <div class="tab-bar">
                <div v-for="(tab, index) in tabs" :key="index"
                    :class="['tab', { active: currentTab === index }]"
                    @click="switchTab(index)">
                    <span>[[ tab.name ]]</span>
                    <button @click.stop="closeTab(index)" class="tab-close">×</button>
                </div>
                <div v-if="tabs.length === 0" class="tab-placeholder">
                    Chưa có chương trình nào được mở
                </div>
            </div>

            <div class="user-info">
                <span style="margin-right: 10px; font-size: 0.8rem; opacity: 0.8;">[[ tabsDebug ]]</span>
                <span>Phiên bản 1.0</span>
            </div>
        </header>

        <!-- Danh sách chương trình -->
        <div class="program-list" v-show="showProgramList">
            <div class="program-list-header">
                <h3>Danh sách chương trình</h3>
                <button @click="showProgramList = false" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="program-items">
                <div v-for="(program, id) in availablePrograms" :key="id"
                    class="program-item" @click="openProgram(id)">
                    <div class="program-icon">
                        <span v-if="program.icon && !program.icon.includes('.')">[[ program.icon ]]</span>
                        <img v-else :src="'/static/img/' + program.icon" alt="Program Icon">
                    </div>
                    <div class="program-info">
                        <h4>[[ program.name ]]</h4>
                        <p>[[ program.description ]]</p>
                    </div>
                </div>
                <div v-if="Object.keys(availablePrograms).length === 0" class="no-programs">
                    Tất cả các chương trình đã mở
                </div>
            </div>
        </div>

        <!-- Nội dung chính -->
        <main>
            <!-- Nội dung tab -->
            <div class="tab-content">
                <div v-for="(tab, index) in tabs" :key="index"
                    :class="['tab-pane', { active: currentTab === index }]">
                    <iframe :src="tab.url" frameborder="0" class="program-frame"
                        :id="'frame-' + tab.id"></iframe>
                </div>

                <div v-if="tabs.length === 0" class="welcome-screen">
                    <div class="welcome-content">
                        <h2>Chào mừng đến với Data Assortment All in One</h2>
                        <p>Chọn một chương trình từ danh sách ở góc trái trên để bắt đầu</p>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <div class="status-bar">
                <div class="app-title">Data Assortment All in One</div>
                <span>Sẵn sàng</span>
            </div>
        </footer>
    </div>

    <script>
        new Vue({
            el: '#app',
            delimiters: ['[[', ']]'],
            data: {
                showProgramList: false,
                tabs: [],
                currentTab: -1,
                availablePrograms: {}
            },
            mounted() {
                this.loadAvailablePrograms();
                // Không sử dụng mock data - chỉ hiển thị lỗi nếu API thất bại
                setTimeout(() => {
                    if (Object.keys(this.availablePrograms).length === 0) {
                        console.error('❌ Không thể tải danh sách chương trình từ server');
                        this.showError('Không thể tải danh sách chương trình. Vui lòng kiểm tra kết nối server.');
                    }
                }, 5000);
            },
            computed: {
                tabsDebug() {
                    return `Tabs: ${this.tabs.length}, Current: ${this.currentTab}`;
                }
            },
            watch: {
                tabs: {
                    handler(newTabs) {
                        console.log('🔍 Tabs array changed:', newTabs.length, newTabs);
                    },
                    deep: true
                }
            },
            methods: {
                loadAvailablePrograms(retryCount = 0) {
                    console.log('🔄 Loading available programs...');
                    axios.get('/api/programs')
                        .then(response => {
                            console.log('✅ Programs loaded:', response.data);
                            this.availablePrograms = response.data;
                        })
                        .catch(error => {
                            console.error('❌ Lỗi khi tải danh sách chương trình:', error);
                            // Retry up to 3 times with delay
                            if (retryCount < 3) {
                                console.log(`🔄 Retrying... (${retryCount + 1}/3)`);
                                setTimeout(() => {
                                    this.loadAvailablePrograms(retryCount + 1);
                                }, 1000);
                            }
                        });
                },
                openProgram(programId) {
                    const program = this.availablePrograms[programId];
                    if (!program) {
                        console.error('❌ Program not found:', programId);
                        return;
                    }

                    console.log('🔄 Opening program:', program.name, 'ID:', programId);
                    console.log('📊 Current tabs before:', this.tabs.length);

                    // Tạo tab mới
                    const tab = {
                        id: programId,
                        name: program.name,
                        url: `/program/${programId}`
                    };

                    // Thêm tab và chuyển đến tab đó
                    this.tabs.push(tab);
                    this.currentTab = this.tabs.length - 1;

                    console.log('📊 Current tabs after:', this.tabs.length);
                    console.log('📊 Current tab index:', this.currentTab);
                    console.log('📊 Tabs array:', this.tabs);

                    // Ẩn chương trình đã mở khỏi danh sách
                    this.$delete(this.availablePrograms, programId);

                    // Đóng menu danh sách
                    this.showProgramList = false;

                    console.log('✅ Program opened successfully');
                },
                switchTab(index) {
                    this.currentTab = index;
                },
                closeTab(index) {
                    const tab = this.tabs[index];
                    console.log('🔄 Closing tab:', tab.name, 'ID:', tab.id);
                    console.log('📊 Tabs before close:', this.tabs.length);

                    // Thông báo cho server rằng chương trình đã đóng TRƯỚC KHI xóa tab
                    axios.post('/api/close_program', { program_id: tab.id })
                        .then(response => {
                            console.log('✅ Program closed successfully:', response.data);
                            // Thêm lại chương trình vào danh sách available
                            let defaultIcon = '🛒'; // Default for basket_arrangement
                            if (tab.id === 'ai_classification') defaultIcon = '🤖';
                            if (tab.id === 'external_update') defaultIcon = '🔄';

                            this.$set(this.availablePrograms, tab.id, {
                                name: tab.name,
                                description: response.data.description || tab.name,
                                icon: response.data.icon || defaultIcon
                            });
                        })
                        .catch(error => {
                            console.error('❌ Lỗi khi đóng chương trình:', error);
                            // Vẫn thêm lại chương trình vào danh sách ngay cả khi có lỗi
                            let defaultIcon = '🛒'; // Default for basket_arrangement
                            if (tab.id === 'ai_classification') defaultIcon = '🤖';
                            if (tab.id === 'external_update') defaultIcon = '🔄';

                            this.$set(this.availablePrograms, tab.id, {
                                name: tab.name,
                                description: tab.name,
                                icon: defaultIcon
                            });
                        });

                    // Xóa tab khỏi UI SAU KHI đã thông báo server
                    this.tabs.splice(index, 1);
                    console.log('📊 Tabs after splice:', this.tabs.length);

                    // Điều chỉnh currentTab
                    if (this.currentTab === index) {
                        if (index > 0) {
                            this.currentTab = index - 1;
                        } else if (this.tabs.length > 0) {
                            this.currentTab = 0;
                        } else {
                            this.currentTab = -1;
                        }
                    } else if (this.currentTab > index) {
                        this.currentTab--;
                    }

                    console.log('📊 Final currentTab:', this.currentTab);
                    console.log('📊 Final tabs array:', this.tabs);
                }
            }
        });
    </script>
</body>
</html>