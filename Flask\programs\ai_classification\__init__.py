from flask import Blueprint, render_template, request, jsonify, session
import traceback
import re
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from gsheet_manager import get_global_manager as get_global_gsheet_manager
from .core.ai_logic import (
    get_database_stats, get_all_classifications,
    process_brand_classifications, auto_update_prices, setup_database
)

# Thông tin chương trình
NAME = "AI Classification"
DESCRIPTION = "Phân loại sản phẩm thông minh bằng AI với OpenAI GPT"
ICON = "🤖"

# Tạo Blueprint
ai_classification_bp = Blueprint('ai_classification', __name__,
                                template_folder='templates',
                                static_folder='static',
                                url_prefix='/ai_classification')

def parse_spreadsheet_id(url):
    """Parse Google Spreadsheet ID from URL"""
    if not url:
        return None

    # Pattern để extract spreadsheet ID từ URL
    patterns = [
        r'/spreadsheets/d/([a-zA-Z0-9-_]+)',
        r'id=([a-zA-Z0-9-_]+)',
        r'^([a-zA-Z0-9-_]+)$'  # Nếu chỉ là ID
    ]

    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)

    return None

def setup(app):
    """Đăng ký blueprint với Flask app"""
    app.register_blueprint(ai_classification_bp)

@ai_classification_bp.route('/')
def index():
    """Trang chính của AI Classification"""
    return render_template('programs/ai_classification.html')

@ai_classification_bp.route('/api/load_sheet', methods=['POST'])
def load_sheet():
    """API để load Google Sheet"""
    try:
        data = request.get_json()
        sheet_url = data.get('sheet_url', '')

        if not sheet_url:
            return jsonify({
                'success': False,
                'error': 'URL Google Sheet không được để trống'
            }), 400

        # Parse spreadsheet ID
        sheet_id = parse_spreadsheet_id(sheet_url)

        if not sheet_id:
            return jsonify({
                'success': False,
                'error': 'URL Google Sheet không hợp lệ. Vui lòng kiểm tra lại URL.'
            }), 400

        # Thử kết nối thực với Google Sheets
        try:
            gsheet_manager = get_global_gsheet_manager()
            print(f"🔍 AI Classification - Manager instance: {gsheet_manager}")
            print(f"🔍 AI Classification - Is authenticated: {gsheet_manager.is_authenticated()}")
            print(f"🔍 AI Classification - Has credentials: {gsheet_manager.credentials is not None}")
            print(f"🔍 AI Classification - Has gc: {gsheet_manager.gc is not None}")

            # Kiểm tra xem đã authenticate chưa
            if not gsheet_manager.is_authenticated():
                print("❌ AI Classification - Not authenticated, trying to force authenticate")
                # Thử force authenticate
                if not gsheet_manager.force_authenticate():
                    print("❌ AI Classification - Force authenticate failed")
                    return jsonify({
                        'success': False,
                        'error': 'Chưa xác thực với Google Sheets API. Vui lòng xác thực trước.',
                        'need_auth': True
                    }), 401
                else:
                    print("✅ AI Classification - Force authenticate success")

            print(f"🔍 AI Classification - Opening spreadsheet: {sheet_id}")
            spreadsheet = gsheet_manager.open_by_key(sheet_id)
            print(f"✅ AI Classification - Spreadsheet opened successfully: {spreadsheet.title}")

            # Lấy danh sách worksheets
            worksheets = spreadsheet.worksheets()
            sheet_names = [ws.title for ws in worksheets]
            spreadsheet_title = spreadsheet.title

            # Lưu thông tin vào session
            session['ai_classification'] = {
                'sheet_id': sheet_id,
                'sheet_names': sheet_names,
                'spreadsheet_title': spreadsheet_title,
                'is_real_connection': True
            }
            session.modified = True

            return jsonify({
                'success': True,
                'data': {
                    'sheet_id': sheet_id,
                    'sheet_names': sheet_names,
                    'total_sheets': len(sheet_names),
                    'spreadsheet_title': spreadsheet_title
                }
            })

        except Exception as e:
            # Trả về lỗi thay vì sử dụng mock data
            return jsonify({
                'success': False,
                'error': f'Không thể kết nối đến Google Sheets: {str(e)}',
                'details': 'Vui lòng kiểm tra lại URL và quyền truy cập Google Sheets.'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@ai_classification_bp.route('/api/get_classifications', methods=['GET'])
def get_classifications():
    """API để lấy danh sách phân loại từ database"""
    try:
        # Đảm bảo database đã được khởi tạo
        setup_database()

        # Lấy tất cả phân loại từ database
        classifications = get_all_classifications()

        return jsonify({
            'success': True,
            'classifications': classifications
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_classification_bp.route('/api/process_classification', methods=['POST'])
def process_classification():
    """API để xử lý phân loại AI"""
    try:
        data = request.get_json()
        brand_sheet = data.get('brand_sheet', 'Brand')
        deal_sheet = data.get('deal_sheet', 'Deal list')
        model = data.get('model', 'gpt-4o-mini')
        last_row = data.get('last_row', 100)

        # Lấy thông tin sheet từ session
        if 'ai_classification' not in session:
            return jsonify({
                'success': False,
                'error': 'Chưa load Google Sheet. Vui lòng load sheet trước.'
            }), 400

        sheet_info = session['ai_classification']
        sheet_id = sheet_info.get('sheet_id')

        if not sheet_id:
            return jsonify({
                'success': False,
                'error': 'Không tìm thấy thông tin sheet trong session.'
            }), 400

        # Lấy column mapping từ session
        ai_session = session.get('ai_classification', {})
        brand_mapping = ai_session.get('brand_mapping', {
            'brand_code': 'A',
            'brand_type': 'E',
            'brand_name': 'D'
        })
        deal_mapping = ai_session.get('deal_mapping', {
            'deal_brand_code': 'A',
            'deal_product': 'H',
            'deal_price': 'AB',
            'deal_pick': 'N'
        })

        try:
            # Sử dụng global GoogleSheetManager
            gsheet_manager = get_global_gsheet_manager()

            # Kiểm tra xem đã authenticate chưa
            if not gsheet_manager.is_authenticated():
                return jsonify({
                    'success': False,
                    'error': 'Chưa xác thực với Google Sheets API. Vui lòng xác thực trước.',
                    'need_auth': True
                }), 401

            spreadsheet = gsheet_manager.open_by_key(sheet_id)

            # Gọi hàm xử lý phân loại thực tế
            result = process_brand_classifications(
                spreadsheet=spreadsheet,
                brand_sheet_name=brand_sheet,
                deal_sheet_name=deal_sheet,
                brand_code_col=brand_mapping.get('brand_code', 'A'),
                brand_type_col=brand_mapping.get('brand_type', 'E'),
                deal_brand_code_col=deal_mapping.get('deal_brand_code', 'A'),
                deal_product_col=deal_mapping.get('deal_product', 'H'),
                last_row=last_row,
                model=model,
                brand_name_col=brand_mapping.get('brand_name', 'D'),
                deal_price_col=deal_mapping.get('deal_price', 'AB'),
                deal_pick_col=deal_mapping.get('deal_pick', 'N')
            )

            return jsonify({
                'success': True,
                'result': result
            })

        except Exception as e:
            # Trả về lỗi thay vì sử dụng mock data
            return jsonify({
                'success': False,
                'error': f'Lỗi khi xử lý phân loại: {str(e)}',
                'details': 'Vui lòng kiểm tra lại dữ liệu và thử lại.',
                'traceback': traceback.format_exc()
            }), 500

        # Code thực tế sẽ như sau (khi có credentials):
        """
        try:
            gsheet_manager = GoogleSheetManager(auth_type='service', credentials_data=credentials)
            spreadsheet = gsheet_manager.open_by_key(sheet_id)

            result = process_brand_classifications(
                spreadsheet=spreadsheet,
                brand_sheet_name=brand_sheet,
                deal_sheet_name=deal_sheet,
                brand_code_col='A',
                brand_type_col='E',
                deal_brand_code_col='A',
                deal_product_col='H',
                last_row=last_row,
                model=model,
                brand_name_col='D',
                deal_price_col='AB',
                deal_pick_col='N'
            )

            return jsonify({
                'success': True,
                'result': result
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            }), 500
        """

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@ai_classification_bp.route('/api/get_column_mapping', methods=['GET'])
def get_column_mapping():
    """API để lấy thông tin mapping cột"""
    try:
        from .core.ai_logic import DEFAULT_BRAND_MAPPING, DEFAULT_DEAL_MAPPING

        # Lấy mapping từ session hoặc sử dụng default
        ai_session = session.get('ai_classification', {})
        brand_mapping = ai_session.get('brand_mapping', DEFAULT_BRAND_MAPPING)
        deal_mapping = ai_session.get('deal_mapping', DEFAULT_DEAL_MAPPING)

        return jsonify({
            'success': True,
            'brand_mapping': brand_mapping,
            'deal_mapping': deal_mapping
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_classification_bp.route('/api/update_column_mapping', methods=['POST'])
def update_column_mapping():
    """API để cập nhật mapping cột"""
    try:
        data = request.get_json()
        brand_mapping = data.get('brand_mapping', {})
        deal_mapping = data.get('deal_mapping', {})

        # Lưu mapping vào session
        if 'ai_classification' not in session:
            session['ai_classification'] = {}
        session['ai_classification']['brand_mapping'] = brand_mapping
        session['ai_classification']['deal_mapping'] = deal_mapping
        session.modified = True

        return jsonify({
            'success': True,
            'message': 'Đã cập nhật mapping cột thành công'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_classification_bp.route('/api/get_database_stats', methods=['GET'])
def get_database_stats_api():
    """API để lấy thống kê database"""
    try:
        # Đảm bảo database đã được khởi tạo
        setup_database()

        # Lấy thống kê thực từ database
        from .core.ai_logic import get_database_stats
        stats = get_database_stats()

        return jsonify({
            'success': True,
            'stats': stats
        })
    except Exception as e:
        # Trả về lỗi thay vì mock data
        return jsonify({
            'success': False,
            'error': f'Lỗi khi lấy thống kê database: {str(e)}',
            'details': 'Vui lòng kiểm tra kết nối database.'
        }), 500

@ai_classification_bp.route('/api/auto_update_prices', methods=['POST'])
def auto_update_prices_api():
    """API để tự động cập nhật giá"""
    try:
        data = request.get_json()

        # Lấy thông tin sheet từ session
        if 'ai_classification' not in session:
            return jsonify({
                'success': False,
                'error': 'Chưa load Google Sheet. Vui lòng load sheet trước.'
            }), 400

        sheet_info = session['ai_classification']
        sheet_id = sheet_info.get('sheet_id')

        if not sheet_id:
            return jsonify({
                'success': False,
                'error': 'Không tìm thấy thông tin sheet trong session.'
            }), 400

        try:
            # Sử dụng global GoogleSheetManager
            gsheet_manager = get_global_gsheet_manager()

            # Kiểm tra xem đã authenticate chưa
            if not gsheet_manager.is_authenticated():
                return jsonify({
                    'success': False,
                    'error': 'Chưa xác thực với Google Sheets API. Vui lòng xác thực trước.',
                    'need_auth': True
                }), 401

            spreadsheet = gsheet_manager.open_by_key(sheet_id)

            # Gọi hàm auto update prices
            result = auto_update_prices(spreadsheet)

            return jsonify({
                'success': True,
                'result': result
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@ai_classification_bp.route('/api/get_all_classifications', methods=['GET'])
def get_all_classifications_api():
    """API để lấy tất cả phân loại"""
    try:
        classifications = get_all_classifications()
        return jsonify({
            'success': True,
            'classifications': classifications
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500