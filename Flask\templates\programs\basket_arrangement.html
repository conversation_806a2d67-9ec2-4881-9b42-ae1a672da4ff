<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basket Arrangement</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('basket_arrangement.static', filename='css/basket_style.css') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .basket-container {
            width: 100%;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }

        .main-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }

        .section {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 15px;
            margin: 20px 0;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .section:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row {
            display: flex;
            align-items: center;
            margin: 15px 0;
            gap: 15px;
            flex-wrap: wrap;
        }

        .form-row label {
            min-width: 120px;
            color: #34495e;
            font-weight: 500;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
            background: white;
        }

        .btn {
            border-radius: 10px;
            padding: 12px 24px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2980b9, #1f5f8b);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #229954, #1e7e34);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
            color: white;
        }

        .condition-row {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 15px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .condition-row:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .condition-row select,
        .condition-row input {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 10px 12px;
            background: white;
            transition: all 0.3s ease;
        }

        .condition-row select:focus,
        .condition-row input:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
            outline: none;
        }

        .id-input {
            flex: 1;
            min-width: 300px;
        }

        .time-selector {
            min-width: 150px;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .remove-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .remove-btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: scale(1.1);
        }

        .log-area {
            background: #2c3e50;
            border: 2px solid #34495e;
            color: #2ecc71;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 13px;
            height: 250px;
            overflow-y: auto;
            padding: 15px;
            white-space: pre-wrap;
            border-radius: 10px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-success {
            background: #28a745;
        }

        .status-error {
            background: #dc3545;
        }

        .status-warning {
            background: #ffc107;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .hidden {
            display: none;
        }

        .id-count {
            color: #777;
            font-size: 12px;
            margin-left: 5px;
        }

        .tool-buttons {
            display: flex;
            gap: 2px;
        }

        .tool-btn {
            background: #414141;
            border: 1px solid #555;
            color: #ddd;
            width: 30px;
            height: 30px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .tool-btn:hover {
            background: #505050;
        }

        .time-selection-popup {
            position: absolute;
            background: #2d2d2d;
            border: 1px solid #555;
            border-radius: 4px;
            padding: 10px;
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
            min-width: 200px;
        }

        .time-checkbox {
            display: block;
            margin: 5px 0;
            color: #fff;
        }

        .time-checkbox input {
            margin-right: 8px;
        }

        .popup-buttons {
            margin-top: 10px;
            text-align: center;
        }

        .popup-buttons button {
            margin: 0 5px;
        }

        .title-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .title-section h1 {
            color: white;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .title-section p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            margin: 0;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .modal.hidden {
            display: none;
        }

        .modal-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            min-width: 400px;
            max-width: 90%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .modal-content h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            text-align: center;
            font-weight: 600;
        }

        .time-selection-popup {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .basket-container {
                padding: 10px;
            }

            .condition-row {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .condition-row > * {
                width: 100%;
            }

            .tool-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="basket-container">
        <!-- Title Section -->
        <div class="title-section">
            <h1><i class="fas fa-shopping-basket"></i> Basket Arrangement</h1>
            <p>Sắp xếp ID thông minh vào Google Sheet với thuật toán tối ưu</p>
        </div>

        <div class="main-card">
            <!-- Google Sheet Information Section -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-table"></i>
                    Google Sheet Information
                </div>

                <div class="form-row">
                    <label><i class="fas fa-link"></i> Spreadsheet URL:</label>
                    <input type="text" id="sheetUrl" class="form-control"
                           placeholder="Nhập Google Spreadsheet URL">
                    <button id="loadSheetBtn" class="btn btn-primary">
                        <i class="fas fa-download"></i> Load Sheet
                    </button>
                </div>

                <div class="form-row">
                    <label><i class="fas fa-file-alt"></i> Sheet:</label>
                    <select id="sheetSelect" class="form-control" disabled>
                        <option value="">Chọn sheet...</option>
                    </select>

                    <label style="margin-left: 20px;"><i class="fas fa-columns"></i> Cột ID đầu:</label>
                    <input type="text" id="firstCol" class="form-control"
                           value="A" maxlength="3" style="width: 80px;">

                    <label style="margin-left: 20px;"><i class="fas fa-hashtag"></i> Số cột:</label>
                    <input type="number" id="numColumns" class="form-control"
                           value="12" min="1" max="50" style="width: 80px;">

                    <button id="mapColumnsBtn" class="btn btn-secondary" disabled>
                        <i class="fas fa-map"></i> Map cột
                    </button>
                </div>
            </div>

            <!-- Conditions Section -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-cogs"></i>
                    Điều kiện sắp xếp
                </div>

                <div id="conditionsContainer">
                    <!-- Các dòng điều kiện sẽ được thêm vào đây -->
                </div>

                <div class="form-row">
                    <button id="addConditionBtn" class="btn btn-success">
                        <i class="fas fa-plus"></i> Thêm điều kiện
                    </button>
                    <button id="processBtn" class="btn btn-primary" style="margin-left: auto;">
                        <i class="fas fa-rocket"></i> Xử lý sắp xếp
                    </button>
                </div>
            </div>

            <!-- Log Section -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-terminal"></i>
                    Log xử lý
                </div>
                <div id="logArea" class="log-area"></div>
                <div class="form-row" style="margin-top: 15px;">
                    <button id="clearLogBtn" class="btn btn-secondary">
                        <i class="fas fa-trash"></i> Xóa log
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Time Selection Popup Template -->
    <div id="timeSelectionPopup" class="time-selection-popup hidden">
        <div id="timeCheckboxes"></div>
        <div class="popup-buttons">
            <button class="btn" onclick="clearAllTimeSelection()">Bỏ chọn tất cả</button>
            <button class="btn btn-primary" onclick="closeTimePopup()">Đóng</button>
        </div>
    </div>

    <!-- Column Mapping Modal -->
    <div id="columnMappingModal" class="modal hidden">
        <div class="modal-content">
            <h3>Thiết lập ánh xạ cột Deal list</h3>
            <div class="form-row">
                <label>Shop ID:</label>
                <input type="text" id="mapShopId" class="form-control" value="A">
            </div>
            <div class="form-row">
                <label>Item ID:</label>
                <input type="text" id="mapItemId" class="form-control" value="B">
            </div>
            <div class="form-row">
                <label>GMV:</label>
                <input type="text" id="mapGmv" class="form-control" value="C">
            </div>
            <div class="form-row">
                <label>Review:</label>
                <input type="text" id="mapReview" class="form-control" value="D">
            </div>
            <div class="form-row">
                <label>NO:</label>
                <input type="text" id="mapNo" class="form-control" value="E">
            </div>
            <div class="form-row">
                <button class="btn btn-primary" onclick="saveColumnMapping()">Lưu</button>
                <button class="btn" onclick="closeColumnMapping()">Hủy</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/basket_arrangement.js') }}"></script>
</body>
</html>
