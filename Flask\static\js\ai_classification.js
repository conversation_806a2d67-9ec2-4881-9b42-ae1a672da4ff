/**
 * AI Classification JavaScript
 * Quản lý giao diện và logic cho chương trình AI Classification
 */

class AIClassificationApp {
    constructor() {
        this.spreadsheet = null;
        this.selectedModel = 'gpt-4o-mini';
        this.isProcessing = false;
        this.currentProgress = 0;
        this.isAuthenticated = false;

        this.initializeEventListeners();
        this.checkAuthentication();
        this.loadDatabaseStats();
    }

    initializeEventListeners() {
        // Load sheet button
        document.getElementById('loadSheetBtn').addEventListener('click', () => {
            this.loadSheet();
        });

        // Model selection
        document.querySelectorAll('.model-option').forEach(option => {
            option.addEventListener('click', (e) => {
                this.selectModel(e.target.dataset.model);
            });
        });

        // Process button
        document.getElementById('processBtn').addEventListener('click', () => {
            this.startProcessing();
        });

        // Clear log button
        document.getElementById('clearLogBtn').addEventListener('click', () => {
            this.clearLog();
        });

        // Export results button
        document.getElementById('exportResultsBtn').addEventListener('click', () => {
            this.exportResults();
        });

        // Map columns button
        document.getElementById('mapColumnsBtn').addEventListener('click', () => {
            this.showColumnMapping();
        });
    }

    async checkAuthentication() {
        try {
            const response = await fetch('/api/global/check_auth');
            const result = await response.json();

            if (result.success) {
                this.isAuthenticated = result.authenticated;
                if (!this.isAuthenticated) {
                    await this.promptForAuthentication();
                }
                return this.isAuthenticated;
            } else {
                console.error('Error checking auth:', result.error);
                return false;
            }
        } catch (error) {
            console.error('Error checking authentication:', error);
            return false;
        }
    }

    async promptForAuthentication() {
        try {
            // Get auth URL
            const response = await fetch('/api/global/get_auth_url');
            const result = await response.json();

            if (result.success) {
                // Show auth modal
                this.showAuthModal(result.auth_url);
            } else {
                this.showError('Lỗi khi lấy URL xác thực: ' + result.error);
            }
        } catch (error) {
            this.showError('Lỗi khi xác thực: ' + error.message);
        }
    }

    showAuthModal(authUrl) {
        const modalHtml = `
            <div class="modal" id="authModal" style="display: block; z-index: 2000;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>🔐 Xác thực Google Sheets</h3>
                    </div>
                    <div class="modal-body">
                        <p>Để sử dụng AI Classification, bạn cần xác thực với Google Sheets API.</p>
                        <div style="background: #3a3a3a; padding: 10px; border-radius: 5px; margin: 10px 0;">
                            <strong>Bước 1:</strong> Click nút bên dưới để mở trang xác thực Google
                        </div>
                        <div style="text-align: center; margin: 15px 0;">
                            <a href="${authUrl}" target="_blank" class="btn btn-primary">
                                🔗 Mở trang xác thực Google
                            </a>
                        </div>
                        <div style="background: #3a3a3a; padding: 10px; border-radius: 5px; margin: 10px 0;">
                            <strong>Bước 2:</strong> Sau khi xác thực, copy authorization code và paste vào ô bên dưới
                        </div>
                        <div style="margin: 15px 0;">
                            <label style="display: block; margin-bottom: 5px;">Authorization Code:</label>
                            <input type="text" id="authCodeInput" class="form-control" placeholder="Paste authorization code here..." style="width: 100%;">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="aiApp.completeAuthentication()">
                            ✅ Hoàn thành xác thực
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('authModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    async completeAuthentication() {
        const authCode = document.getElementById('authCodeInput').value.trim();

        if (!authCode) {
            this.showError('Vui lòng nhập authorization code');
            return;
        }

        try {
            const response = await fetch('/api/global/complete_auth', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ auth_code: authCode })
            });
            const result = await response.json();

            if (result.success) {
                this.isAuthenticated = true;
                this.log('✅ Xác thực thành công!');

                // Close modal
                const authModal = document.getElementById('authModal');
                if (authModal) {
                    authModal.remove();
                }
            } else {
                this.showError('Xác thực thất bại: ' + result.error);
            }
        } catch (error) {
            this.showError('Lỗi khi hoàn thành xác thực: ' + error.message);
        }
    }

    selectModel(model) {
        this.selectedModel = model;

        // Update UI
        document.querySelectorAll('.model-option').forEach(option => {
            option.classList.remove('active');
        });
        document.querySelector(`[data-model="${model}"]`).classList.add('active');

        this.log(`🤖 Đã chọn model: ${model}`);
    }

    async loadSheet() {
        const sheetUrl = document.getElementById('sheetUrl').value.trim();
        if (!sheetUrl) {
            this.showError('Vui lòng nhập URL Google Sheet!');
            return;
        }

        // Check authentication first
        if (!await this.checkAuthentication()) {
            return;
        }

        this.setLoading(true);
        this.log('🔄 Đang tải Google Sheet...');

        try {
            const response = await fetch('/ai_classification/api/load_sheet', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sheet_url: sheetUrl
                })
            });

            const result = await response.json();

            if (result.success) {
                this.spreadsheet = result.data;
                this.populateSheetSelectors(result.data.sheet_names);

                if (result.warning) {
                    this.log(`⚠️ ${result.warning}`);
                    this.log('✅ Đã tải Google Sheet thành công (mock mode)!');
                } else {
                    this.log('✅ Đã tải Google Sheet thành công!');
                }

                this.log(`📊 Spreadsheet: ${result.data.spreadsheet_title}`);
                this.log(`📋 Tìm thấy ${result.data.total_sheets} sheets: ${result.data.sheet_names.join(', ')}`);

                // Enable controls
                document.getElementById('brandSheetSelect').disabled = false;
                document.getElementById('dealSheetSelect').disabled = false;
                document.getElementById('mapColumnsBtn').disabled = false;
            } else {
                this.showError(`Lỗi tải sheet: ${result.error}`);

                // If authentication error, prompt for auth
                if (result.need_auth) {
                    await this.promptForAuthentication();
                }
            }
        } catch (error) {
            this.showError(`Lỗi kết nối: ${error.message}`);
        } finally {
            this.setLoading(false);
        }
    }

    populateSheetSelectors(sheetNames) {
        const brandSelect = document.getElementById('brandSheetSelect');
        const dealSelect = document.getElementById('dealSheetSelect');

        // Clear existing options
        brandSelect.innerHTML = '<option value="">Chọn sheet Brand...</option>';
        dealSelect.innerHTML = '<option value="">Chọn sheet Deal list...</option>';

        // Add sheet options
        sheetNames.forEach(name => {
            const brandOption = new Option(name, name);
            const dealOption = new Option(name, name);

            brandSelect.add(brandOption);
            dealSelect.add(dealOption);

            // Auto-select based on name patterns
            if (name.toLowerCase().includes('brand')) {
                brandSelect.value = name;
            }
            if (name.toLowerCase().includes('deal')) {
                dealSelect.value = name;
            }
        });
    }

    async startProcessing() {
        if (this.isProcessing) {
            this.showError('Đang xử lý, vui lòng đợi...');
            return;
        }

        // Check authentication first
        if (!await this.checkAuthentication()) {
            return;
        }

        const brandSheet = document.getElementById('brandSheetSelect').value;
        const dealSheet = document.getElementById('dealSheetSelect').value;
        const lastRow = parseInt(document.getElementById('lastRow').value);

        if (!brandSheet || !dealSheet) {
            this.showError('Vui lòng chọn cả Brand sheet và Deal sheet!');
            return;
        }

        this.isProcessing = true;
        this.setLoading(true);
        this.updateProgress(0);

        this.log('🚀 Bắt đầu quá trình phân loại AI...');
        this.log(`📊 Model: ${this.selectedModel}, Số dòng: ${lastRow}`);

        try {
            const response = await fetch('/ai_classification/api/process_classification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    brand_sheet: brandSheet,
                    deal_sheet: dealSheet,
                    model: this.selectedModel,
                    last_row: lastRow
                })
            });

            const result = await response.json();

            if (result.success) {
                this.handleProcessingSuccess(result.result);
            } else {
                this.showError(`Lỗi xử lý: ${result.error}`);
            }
        } catch (error) {
            this.showError(`Lỗi kết nối: ${error.message}`);
        } finally {
            this.isProcessing = false;
            this.setLoading(false);
            this.updateProgress(100);
        }
    }

    handleProcessingSuccess(result) {
        this.log('✅ Hoàn tất quá trình phân loại!');
        this.log(`📊 Đã xử lý: ${result.processed_count} | Cập nhật: ${result.updated_count}`);
        this.log(`🤖 API calls: ${result.api_calls} | Tokens: ${result.tokens_used}`);

        // Update statistics
        this.updateStats({
            apiCalls: result.api_calls,
            tokensUsed: result.tokens_used
        });

        // Enable export button
        document.getElementById('exportResultsBtn').disabled = false;

        // Show classifications
        if (result.classifications && result.classifications.length > 0) {
            this.log('\n📋 Kết quả phân loại:');
            result.classifications.forEach(item => {
                this.log(`  ${item.brand_code}: ${item.classification} (${item.products.length} sản phẩm)`);
            });
        }
    }

    async loadDatabaseStats() {
        try {
            const response = await fetch('/ai_classification/api/get_database_stats');
            const result = await response.json();

            if (result.success) {
                this.updateStats(result.stats);
            }
        } catch (error) {
            console.error('Error loading database stats:', error);
        }
    }

    updateStats(stats) {
        if (stats.total_products !== undefined) {
            document.getElementById('totalProducts').textContent = stats.total_products;
        }
        if (stats.total_brands !== undefined) {
            document.getElementById('totalBrands').textContent = stats.total_brands;
        }
        if (stats.apiCalls !== undefined) {
            document.getElementById('apiCalls').textContent = stats.apiCalls;
        }
        if (stats.tokensUsed !== undefined) {
            document.getElementById('tokensUsed').textContent = stats.tokensUsed;
        }
    }

    updateProgress(percentage) {
        this.currentProgress = percentage;
        document.getElementById('progressFill').style.width = `${percentage}%`;
    }

    async showColumnMapping() {
        // Load current mapping
        await this.loadColumnMapping();

        // Show modal
        document.getElementById('columnMappingModal').style.display = 'block';
    }

    async loadColumnMapping() {
        try {
            const response = await fetch('/ai_classification/api/get_column_mapping');
            const result = await response.json();

            if (result.success) {
                // Brand mapping
                if (result.brand_mapping) {
                    document.getElementById('brandCodeCol').value = result.brand_mapping.brand_code || 'A';
                    document.getElementById('brandTypeCol').value = result.brand_mapping.brand_type || 'E';
                    document.getElementById('brandNameCol').value = result.brand_mapping.brand_name || 'D';
                }

                // Deal mapping
                if (result.deal_mapping) {
                    document.getElementById('dealBrandCodeCol').value = result.deal_mapping.deal_brand_code || 'A';
                    document.getElementById('dealProductCol').value = result.deal_mapping.deal_product || 'H';
                    document.getElementById('dealPriceCol').value = result.deal_mapping.deal_price || 'AB';
                    document.getElementById('dealPickCol').value = result.deal_mapping.deal_pick || 'N';
                }
            }
        } catch (error) {
            console.error('Error loading column mapping:', error);
        }
    }

    async saveColumnMapping() {
        const brandMapping = {
            brand_code: document.getElementById('brandCodeCol').value.trim(),
            brand_type: document.getElementById('brandTypeCol').value.trim(),
            brand_name: document.getElementById('brandNameCol').value.trim()
        };

        const dealMapping = {
            deal_brand_code: document.getElementById('dealBrandCodeCol').value.trim(),
            deal_product: document.getElementById('dealProductCol').value.trim(),
            deal_price: document.getElementById('dealPriceCol').value.trim(),
            deal_pick: document.getElementById('dealPickCol').value.trim()
        };

        try {
            const response = await fetch('/ai_classification/api/update_column_mapping', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    brand_mapping: brandMapping,
                    deal_mapping: dealMapping
                })
            });

            const result = await response.json();

            if (result.success) {
                this.log('✅ Đã lưu mapping cột thành công!');
                this.closeColumnMappingModal();
            } else {
                this.showError(`Lỗi lưu mapping: ${result.error}`);
            }
        } catch (error) {
            this.showError(`Lỗi kết nối: ${error.message}`);
        }
    }

    closeColumnMappingModal() {
        document.getElementById('columnMappingModal').style.display = 'none';
    }

    exportResults() {
        // This would export the results to a file
        // For now, just show a simple alert
        alert('Chức năng xuất kết quả sẽ được triển khai trong phiên bản tiếp theo.');
    }

    log(message) {
        const logArea = document.getElementById('logArea');
        const timestamp = new Date().toLocaleTimeString();
        logArea.textContent += `[${timestamp}] ${message}\n`;
        logArea.scrollTop = logArea.scrollHeight;
    }

    clearLog() {
        document.getElementById('logArea').textContent = '';
    }

    setLoading(isLoading) {
        const container = document.querySelector('.ai-container');
        if (isLoading) {
            container.classList.add('loading');
        } else {
            container.classList.remove('loading');
        }
    }

    showError(message) {
        this.log(`❌ ${message}`);
        // Could also show a toast notification or modal
    }
}

// Global app instance
let aiApp;

// Global functions for modal
function closeColumnMappingModal() {
    if (aiApp) {
        aiApp.closeColumnMappingModal();
    }
}

function saveColumnMapping() {
    if (aiApp) {
        aiApp.saveColumnMapping();
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    aiApp = new AIClassificationApp();
});
