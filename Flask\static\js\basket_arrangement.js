// Basket Arrangement JavaScript Logic
class BasketArrangement {
    constructor() {
        this.timeSlots = [];
        this.conditions = [];
        this.currentPopup = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.addInitialCondition();
    }

    bindEvents() {
        // Load sheet button
        document.getElementById('loadSheetBtn').addEventListener('click', () => {
            this.loadSheet();
        });

        // Add condition button
        document.getElementById('addConditionBtn').addEventListener('click', () => {
            this.addCondition();
        });

        // Process button
        document.getElementById('processBtn').addEventListener('click', () => {
            this.processArrangement();
        });

        // Clear log button
        document.getElementById('clearLogBtn').addEventListener('click', () => {
            this.clearLog();
        });

        // Map columns button
        document.getElementById('mapColumnsBtn').addEventListener('click', () => {
            this.showColumnMapping();
        });

        // Auto parse sheet URL
        document.getElementById('sheetUrl').addEventListener('input', (e) => {
            this.autoParseSheetId(e.target.value);
        });

        // Close popup when clicking outside
        document.addEventListener('click', (e) => {
            if (this.currentPopup && !this.currentPopup.contains(e.target)) {
                this.closeTimePopup();
            }
        });
    }

    async loadSheet() {
        const sheetUrl = document.getElementById('sheetUrl').value.trim();
        if (!sheetUrl) {
            this.showError('Vui lòng nhập URL của Google Spreadsheet');
            return;
        }

        this.setLoading(true);
        this.log('Đang load Google Spreadsheet...');

        try {
            const response = await fetch('/basket_arrangement/api/load_sheet', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sheet_url: sheetUrl
                })
            });

            const result = await response.json();

            if (result.success) {
                this.populateSheetSelect(result.data.sheet_names);
                this.log(`✅ Đã load thành công ${result.data.total_sheets} sheets`);
                document.getElementById('mapColumnsBtn').disabled = false;
            } else {
                this.showError(result.error);
                this.log(`❌ Lỗi: ${result.error}`);
            }
        } catch (error) {
            this.showError('Lỗi kết nối: ' + error.message);
            this.log(`❌ Lỗi kết nối: ${error.message}`);
        } finally {
            this.setLoading(false);
        }
    }

    populateSheetSelect(sheetNames) {
        const select = document.getElementById('sheetSelect');
        select.innerHTML = '<option value="">Chọn sheet...</option>';

        sheetNames.forEach(name => {
            const option = document.createElement('option');
            option.value = name;
            option.textContent = name;
            if (name === 'Basket') {
                option.selected = true;
            }
            select.appendChild(option);
        });

        select.disabled = false;

        // Auto load time slots if Basket sheet is selected
        if (sheetNames.includes('Basket')) {
            this.loadTimeSlots();
        }
    }

    async loadTimeSlots() {
        const sheetName = document.getElementById('sheetSelect').value || 'Basket';
        const firstCol = document.getElementById('firstCol').value || 'A';
        const numColumns = parseInt(document.getElementById('numColumns').value) || 12;

        try {
            const response = await fetch('/basket_arrangement/api/get_time_slots', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sheet_name: sheetName,
                    first_col: firstCol,
                    num_columns: numColumns
                })
            });

            const result = await response.json();

            if (result.success) {
                this.timeSlots = result.data.time_slots;
                this.updateTimeSelectors();
                this.log(`📅 Đã load ${this.timeSlots.length} khung giờ: ${this.timeSlots.join(', ')}`);
            } else {
                this.showError(result.error);
                this.log(`❌ Lỗi load khung giờ: ${result.error}`);
            }
        } catch (error) {
            this.showError('Lỗi khi load khung giờ: ' + error.message);
        }
    }

    addInitialCondition() {
        this.addCondition();
    }

    addCondition() {
        const container = document.getElementById('conditionsContainer');
        const conditionId = 'condition_' + Date.now();

        const conditionRow = document.createElement('div');
        conditionRow.className = 'condition-row';
        conditionRow.id = conditionId;

        conditionRow.innerHTML = `
            <select class="top-select">
                <option value="Top 20">Top 20</option>
                <option value="Top 30">Top 30</option>
                <option value="Top 50">Top 50</option>
                <option value="Top 100">Top 100</option>
                <option value="Top 150">Top 150</option>
                <option value="Top 200">Top 200</option>
                <option value="Top 250">Top 250</option>
                <option value="Top 150↓">Top 150↓</option>
                <option value="Top 200↓">Top 200↓</option>
                <option value="Top 250↓">Top 250↓</option>
            </select>

            <div style="flex: 1; display: flex; align-items: center; gap: 5px;">
                <input type="text" class="id-input form-control"
                       placeholder="Nhập hoặc dán các ID (tự động nhận diện định dạng)">
                <span class="id-count">0 ID</span>
                <div class="tool-buttons">
                    <button class="tool-btn" title="Dán từ clipboard" onclick="pasteFromClipboard('${conditionId}')">📋</button>
                    <button class="tool-btn" title="Loại bỏ trùng lặp" onclick="removeDuplicates('${conditionId}')">🔄</button>
                </div>
            </div>

            <div class="checkbox-container">
                <input type="checkbox" class="group-checkbox">
                <label>Nhóm</label>
            </div>

            <button class="time-selector btn" onclick="showTimeSelection('${conditionId}')">
                Chọn khung giờ
            </button>

            <button class="remove-btn" onclick="removeCondition('${conditionId}')">×</button>
        `;

        container.appendChild(conditionRow);

        // Bind events for this condition
        this.bindConditionEvents(conditionId);

        this.conditions.push({
            id: conditionId,
            selectedTimes: []
        });
    }

    bindConditionEvents(conditionId) {
        const row = document.getElementById(conditionId);
        const idInput = row.querySelector('.id-input');
        const idCount = row.querySelector('.id-count');

        // Update ID count when input changes
        idInput.addEventListener('input', () => {
            const ids = this.parseIds(idInput.value);
            idCount.textContent = `${ids.length} ID`;
        });

        // Auto format IDs
        idInput.addEventListener('blur', () => {
            const formatted = this.formatIds(idInput.value);
            if (formatted !== idInput.value) {
                idInput.value = formatted;
                const ids = this.parseIds(formatted);
                idCount.textContent = `${ids.length} ID`;
            }
        });
    }

    parseIds(text) {
        if (!text) return [];

        // Replace various separators with comma
        text = text.replace(/[\t\r\n;]+/g, ',');
        text = text.replace(/\s{2,}/g, ',');
        text = text.replace(/(\S+)\s+(\S+)/g, '$1,$2');
        text = text.replace(/,+/g, ',');
        text = text.trim().replace(/^,|,$/g, '');
        text = text.replace(/\s*,\s*/g, ',');

        return text.split(',').filter(id => id.trim()).map(id => id.trim());
    }

    formatIds(text) {
        const ids = this.parseIds(text);
        return ids.join(',');
    }

    updateTimeSelectors() {
        // Update all time selector buttons
        document.querySelectorAll('.time-selector').forEach(btn => {
            if (btn.textContent === 'Chọn khung giờ') {
                // Keep default text if no time selected
            }
        });
    }

    showTimeSelection(conditionId) {
        const popup = document.getElementById('timeSelectionPopup');
        const checkboxContainer = document.getElementById('timeCheckboxes');
        const btn = document.querySelector(`#${conditionId} .time-selector`);

        // Clear existing checkboxes
        checkboxContainer.innerHTML = '';

        // Get current selection
        const condition = this.conditions.find(c => c.id === conditionId);
        const selectedTimes = condition ? condition.selectedTimes : [];

        // Create checkboxes for each time slot
        this.timeSlots.forEach(timeSlot => {
            const label = document.createElement('label');
            label.className = 'time-checkbox';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.value = timeSlot;
            checkbox.checked = selectedTimes.includes(timeSlot);

            checkbox.addEventListener('change', () => {
                this.updateTimeSelection(conditionId);
            });

            label.appendChild(checkbox);
            label.appendChild(document.createTextNode(timeSlot));
            checkboxContainer.appendChild(label);
        });

        // Position popup
        const rect = btn.getBoundingClientRect();
        popup.style.left = rect.left + 'px';
        popup.style.top = (rect.bottom + 5) + 'px';
        popup.classList.remove('hidden');

        this.currentPopup = popup;
        popup.dataset.conditionId = conditionId;
    }

    updateTimeSelection(conditionId) {
        const popup = document.getElementById('timeSelectionPopup');
        const checkboxes = popup.querySelectorAll('input[type="checkbox"]');
        const selectedTimes = [];

        checkboxes.forEach(cb => {
            if (cb.checked) {
                selectedTimes.push(cb.value);
            }
        });

        // Update condition data
        const condition = this.conditions.find(c => c.id === conditionId);
        if (condition) {
            condition.selectedTimes = selectedTimes;
        }

        // Update button text
        const btn = document.querySelector(`#${conditionId} .time-selector`);
        if (selectedTimes.length === 0) {
            btn.textContent = 'Chọn khung giờ';
        } else if (selectedTimes.length === 1) {
            btn.textContent = selectedTimes[0];
        } else {
            btn.textContent = `${selectedTimes.length} khung giờ đã chọn`;
        }
    }

    closeTimePopup() {
        const popup = document.getElementById('timeSelectionPopup');
        popup.classList.add('hidden');
        this.currentPopup = null;
    }

    clearAllTimeSelection() {
        const popup = document.getElementById('timeSelectionPopup');
        const checkboxes = popup.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => cb.checked = false);

        const conditionId = popup.dataset.conditionId;
        this.updateTimeSelection(conditionId);
    }

    removeCondition(conditionId) {
        const element = document.getElementById(conditionId);
        if (element) {
            element.remove();
        }

        this.conditions = this.conditions.filter(c => c.id !== conditionId);
    }

    async processArrangement() {
        // Validate inputs
        if (!this.validateInputs()) {
            return;
        }

        this.setLoading(true);
        this.log('🚀 Bắt đầu xử lý sắp xếp...');

        try {
            const conditions = this.collectConditions();
            const sheetConfig = this.getSheetConfig();

            const response = await fetch('/basket_arrangement/api/process_arrangement', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    conditions: conditions,
                    sheet_config: sheetConfig
                })
            });

            const result = await response.json();

            if (result.success) {
                this.log('✅ ' + result.result.message);

                // Display results
                Object.entries(result.result.results).forEach(([timeSlot, data]) => {
                    this.log(`📊 ${timeSlot}: ${data.original_count} → ${data.new_count} ID (${data.added_count >= 0 ? '+' : ''}${data.added_count})`);
                });

                // Display logs from backend
                if (result.result.logs) {
                    result.result.logs.forEach(log => this.log(log));
                }
            } else {
                this.showError(result.error);
                this.log(`❌ Lỗi: ${result.error}`);
            }
        } catch (error) {
            this.showError('Lỗi kết nối: ' + error.message);
            this.log(`❌ Lỗi kết nối: ${error.message}`);
        } finally {
            this.setLoading(false);
        }
    }

    validateInputs() {
        const sheetSelect = document.getElementById('sheetSelect');
        if (!sheetSelect.value) {
            this.showError('Vui lòng chọn sheet');
            return false;
        }

        const hasValidCondition = this.conditions.some(condition => {
            const row = document.getElementById(condition.id);
            const idInput = row.querySelector('.id-input');
            const ids = this.parseIds(idInput.value);
            return ids.length > 0 && condition.selectedTimes.length > 0;
        });

        if (!hasValidCondition) {
            this.showError('Vui lòng thêm ít nhất một điều kiện hợp lệ (có ID và khung giờ)');
            return false;
        }

        return true;
    }

    collectConditions() {
        const conditions = [];

        this.conditions.forEach(condition => {
            const row = document.getElementById(condition.id);
            if (!row) return;

            const topSelect = row.querySelector('.top-select');
            const idInput = row.querySelector('.id-input');
            const groupCheckbox = row.querySelector('.group-checkbox');

            const ids = this.parseIds(idInput.value);

            if (ids.length > 0 && condition.selectedTimes.length > 0) {
                conditions.push({
                    top_limit: topSelect.value,
                    input_ids: ids,
                    selected_times: condition.selectedTimes,
                    is_grouped: groupCheckbox.checked
                });
            }
        });

        return conditions;
    }

    getSheetConfig() {
        return {
            basket_sheet_name: document.getElementById('sheetSelect').value,
            deal_sheet_name: 'Deal list',
            first_col: document.getElementById('firstCol').value,
            num_columns: parseInt(document.getElementById('numColumns').value),
            column_mapping: this.getColumnMapping()
        };
    }

    getColumnMapping() {
        // Return default mapping, can be extended to read from UI
        return {
            shop_id: 'A',
            item_id: 'B',
            gmv: 'C',
            review: 'D',
            no: 'E'
        };
    }

    showColumnMapping() {
        // Show column mapping modal (simplified for now)
        alert('Chức năng Map cột sẽ được triển khai trong phiên bản tiếp theo');
    }

    autoParseSheetId(url) {
        // Auto-extract sheet ID from URL
        const patterns = [
            /\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/,
            /key=([a-zA-Z0-9-_]+)/,
            /^([a-zA-Z0-9-_]+)$/
        ];

        for (const pattern of patterns) {
            const match = url.match(pattern);
            if (match) {
                // Valid sheet ID found
                break;
            }
        }
    }

    setLoading(loading) {
        const container = document.querySelector('.basket-container');
        if (loading) {
            container.classList.add('loading');
        } else {
            container.classList.remove('loading');
        }
    }

    log(message) {
        const logArea = document.getElementById('logArea');
        const timestamp = new Date().toLocaleTimeString();
        logArea.textContent += `[${timestamp}] ${message}\n`;
        logArea.scrollTop = logArea.scrollHeight;
    }

    clearLog() {
        document.getElementById('logArea').textContent = '';
    }

    showError(message) {
        alert('Lỗi: ' + message);
    }
}

// Global functions for onclick handlers
function pasteFromClipboard(conditionId) {
    navigator.clipboard.readText().then(text => {
        const row = document.getElementById(conditionId);
        const idInput = row.querySelector('.id-input');
        const formatted = basketApp.formatIds(text);
        idInput.value = formatted;

        const ids = basketApp.parseIds(formatted);
        const idCount = row.querySelector('.id-count');
        idCount.textContent = `${ids.length} ID`;
    }).catch(err => {
        console.error('Không thể dán từ clipboard:', err);
    });
}

function removeDuplicates(conditionId) {
    const row = document.getElementById(conditionId);
    const idInput = row.querySelector('.id-input');
    const ids = basketApp.parseIds(idInput.value);
    const uniqueIds = [...new Set(ids)];

    if (uniqueIds.length !== ids.length) {
        idInput.value = uniqueIds.join(',');
        const idCount = row.querySelector('.id-count');
        idCount.textContent = `${uniqueIds.length} ID`;
        alert(`Đã loại bỏ ${ids.length - uniqueIds.length} ID trùng lặp`);
    } else {
        alert('Không có ID trùng lặp');
    }
}

function removeCondition(conditionId) {
    basketApp.removeCondition(conditionId);
}

function showTimeSelection(conditionId) {
    basketApp.showTimeSelection(conditionId);
}

function clearAllTimeSelection() {
    basketApp.clearAllTimeSelection();
}

function closeTimePopup() {
    basketApp.closeTimePopup();
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.basketApp = new BasketArrangement();
});
