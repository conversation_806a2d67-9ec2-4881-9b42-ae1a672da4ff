from flask import Flask, render_template, jsonify, request, session
import os
import importlib
import sys
from pathlib import Path

app = Flask(__name__)
app.secret_key = 'your_secret_key_here'  # <PERSON><PERSON><PERSON> thành một secret key an toàn hơn

# <PERSON><PERSON>ng ký các program module
def register_programs():
    programs_dir = Path(__file__).parent / 'programs'
    print(f"🔍 Looking for programs in: {programs_dir}")
    if not programs_dir.exists():
        print("❌ Programs directory does not exist")
        return

    app.programs = {}
    for item in programs_dir.iterdir():
        print(f"📁 Found item: {item.name}")
        if item.is_dir() and (item / '__init__.py').exists():
            program_name = item.name
            print(f"🔄 Loading program: {program_name}")
            try:
                module_path = f"programs.{program_name}"
                module = importlib.import_module(module_path)
                print(f"✅ Successfully imported {module_path}")

                # <PERSON><PERSON><PERSON> tra các thu<PERSON><PERSON> t<PERSON> cần thiết
                name = getattr(module, 'NAME', program_name)
                description = getattr(module, 'DESCRIPTION', '')
                icon = getattr(module, 'ICON', '📦')

                print(f"📋 Program info: {name} - {description} - {icon}")

                if hasattr(module, 'setup'):
                    module.setup(app)
                    print(f"✅ Setup function called for {program_name}")
                else:
                    print(f"⚠️ No setup function found for {program_name}")

                app.programs[program_name] = {
                    'name': name,
                    'description': description,
                    'icon': icon,
                    'active': False
                }
                print(f"✅ Program {program_name} registered successfully")
            except Exception as e:
                print(f"❌ Không thể nạp chương trình {program_name}: {str(e)}")
                import traceback
                traceback.print_exc()
                # Vẫn thêm program với thông tin cơ bản để debug
                app.programs[program_name] = {
                    'name': program_name,
                    'description': f'Error loading: {str(e)}',
                    'icon': '❌',
                    'active': False
                }

# Gọi register_programs khi khởi tạo app
register_programs()

@app.route('/')
def index():
    return render_template('index.html', programs=getattr(app, 'programs', {}))

@app.route('/program/<program_id>')
def program_ui(program_id):
    print(f"🔄 Opening program: {program_id}")

    if not hasattr(app, 'programs') or program_id not in app.programs:
        print(f"❌ Program {program_id} not found")
        return "Chương trình không tồn tại", 404

    app.programs[program_id]['active'] = True

    # Đảm bảo session được khởi tạo
    if 'active_programs' not in session:
        session['active_programs'] = []

    if program_id not in session['active_programs']:
        session['active_programs'].append(program_id)
        print(f"✅ Added {program_id} to active programs")

    # Đảm bảo session được lưu
    session.modified = True

    print(f"📋 Current active programs: {session.get('active_programs', [])}")

    # Trả về nội dung UI của chương trình
    # External Update sử dụng blueprint riêng
    if program_id == 'external_update':
        from flask import redirect, url_for
        return redirect(url_for('external_update.index'))

    return render_template(f'programs/{program_id}.html')

@app.route('/api/programs')
def get_programs():
    if not hasattr(app, 'programs'):
        print("❌ No programs attribute found")
        return jsonify({})

    active_programs = session.get('active_programs', [])
    print(f"📋 Active programs in session: {active_programs}")
    print(f"📦 All programs: {list(app.programs.keys())}")

    available_programs = {
        k: v for k, v in app.programs.items()
        if k not in active_programs
    }

    print(f"✅ Available programs: {list(available_programs.keys())}")
    return jsonify(available_programs)

@app.route('/api/close_program', methods=['POST'])
def close_program():
    program_id = request.json.get('program_id')
    print(f"🔄 Closing program: {program_id}")

    if hasattr(app, 'programs') and program_id in app.programs:
        app.programs[program_id]['active'] = False

        # Đảm bảo session được khởi tạo
        if 'active_programs' not in session:
            session['active_programs'] = []

        # Xóa program khỏi danh sách active
        if program_id in session['active_programs']:
            session['active_programs'].remove(program_id)
            print(f"✅ Removed {program_id} from active programs")

        # Đảm bảo session được lưu
        session.modified = True

        print(f"📋 Current active programs: {session.get('active_programs', [])}")

        # Trả về thông tin program để frontend có thể sử dụng
        program_info = app.programs.get(program_id, {})
        return jsonify({
            "success": True,
            "program": program_info,
            "description": program_info.get('description', ''),
            "icon": program_info.get('icon', '')
        })

    return jsonify({"success": True})

if __name__ == '__main__':
    print("🚀 Starting Flask app...")
    print("📁 Programs loaded:", getattr(app, 'programs', {}))
    print("🌐 Server will start at: http://localhost:5000")
    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    except Exception as e:
        print(f"❌ Error starting Flask app: {e}")
        import traceback
        traceback.print_exc()