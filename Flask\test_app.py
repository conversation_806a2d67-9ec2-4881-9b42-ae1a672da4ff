#!/usr/bin/env python3
"""
Test script để kiểm tra xem app có load được không
"""

import sys
import os
from pathlib import Path

# Thêm thư mục hiện tại vào Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

print("🔍 Testing Flask app loading...")
print(f"📁 Current directory: {current_dir}")
print(f"🐍 Python path: {sys.path[:3]}")

try:
    print("\n1. Testing basic imports...")
    from flask import Flask
    print("✅ Flask import successful")
    
    print("\n2. Testing app import...")
    import app
    print("✅ App import successful")
    
    print(f"\n3. Checking programs loaded...")
    if hasattr(app.app, 'programs'):
        programs = app.app.programs
        print(f"✅ Found {len(programs)} programs:")
        for prog_id, prog_info in programs.items():
            status = "✅" if "Error loading" not in prog_info.get('description', '') else "❌"
            print(f"  {status} {prog_id}: {prog_info.get('name', 'Unknown')} - {prog_info.get('description', 'No description')}")
    else:
        print("❌ No programs attribute found")
    
    print(f"\n4. Testing API endpoint...")
    with app.app.test_client() as client:
        response = client.get('/api/programs')
        print(f"✅ API response status: {response.status_code}")
        if response.status_code == 200:
            data = response.get_json()
            print(f"✅ API returned {len(data)} programs")
            for prog_id, prog_info in data.items():
                print(f"  📦 {prog_id}: {prog_info.get('name', 'Unknown')}")
        else:
            print(f"❌ API error: {response.data}")
            
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("\n🏁 Test completed!")
