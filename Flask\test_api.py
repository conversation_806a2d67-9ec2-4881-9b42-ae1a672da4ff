#!/usr/bin/env python3
"""
Test API endpoints
"""

import requests
import json

def test_api():
    base_url = "http://localhost:5000"
    
    print("🔍 Testing API endpoints...")
    
    try:
        # Test main page
        print("\n1. Testing main page...")
        response = requests.get(base_url, timeout=5)
        print(f"✅ Main page status: {response.status_code}")
        
        # Test programs API
        print("\n2. Testing /api/programs...")
        response = requests.get(f"{base_url}/api/programs", timeout=5)
        print(f"✅ Programs API status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Found {len(data)} programs:")
            for prog_id, prog_info in data.items():
                print(f"  📦 {prog_id}: {prog_info.get('name', 'Unknown')} - {prog_info.get('description', 'No description')}")
        else:
            print(f"❌ API error: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Is it running?")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_api()
